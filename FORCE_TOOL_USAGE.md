# Forced Tool Usage Feature

This document explains the forced tool usage feature implemented in the Enhanced MCP Manager.

## Overview

The forced tool usage feature ensures that the AI model uses available tools on every user query to fetch fresh, up-to-date information. This prevents the model from relying on cached or previous conversation context when fresh data is available through tools.

## Configuration

### Environment Variable

Set the `FORCE_TOOL_USAGE` environment variable to control this behavior:

```bash
# Enable forced tool usage (default)
export FORCE_TOOL_USAGE=true

# Disable forced tool usage
export FORCE_TOOL_USAGE=false
```

### Default Behavior

- **Default**: `FORCE_TOOL_USAGE=true` (forced tool usage enabled)
- When enabled, the model MUST use tools before providing any response
- When disabled, the model uses tools only when it determines they are needed

## Implementation Details

### 1. System Message Enhancement

When `FORCE_TOOL_USAGE=true`, the system message includes explicit instructions:

```
IMPORTANT: You MUST use tools for EVERY user query to fetch fresh, up-to-date information.
Do NOT rely on previous conversation context or cached data.
Always call appropriate tools first before providing any answer, even for follow-up questions.
Treat each query as requiring fresh data retrieval.
Use tools iteratively and comprehensively to gather the most current information available.
```

### 2. Tool Choice Configuration

- **Forced mode**: `{"toolChoice": {"any": {}}}` - Model must select a tool
- **Optional mode**: `{"toolChoice": {"auto": {}}}` - Model decides whether to use tools

### 3. Conversation Loop Enforcement

The conversation execution includes several enforcement mechanisms:

#### Retry Logic
If the model tries to respond without using tools when they're available:
- On the first iteration, the system message is enhanced with additional instructions
- The request is retried with the enhanced prompt

#### Warning Indicators
If tools were available but not used, responses are prefixed with:
```
[Note: Response generated without fresh data retrieval]
```

#### Tool Call Tracking
The system tracks whether any tools were called during the conversation and enforces usage accordingly.

## Code Changes Summary

### 1. Constructor Enhancement
```python
def __init__(self, *args, **kwargs):
    super().__init__(*args, **kwargs)
    # Force tool usage setting
    self.force_tool_usage = os.getenv("FORCE_TOOL_USAGE", "true").lower() == "true"
```

### 2. System Message Builder
```python
def _build_context_aware_system_message(self, chat_session, tools_available=None):
    if tools_available and self.force_tool_usage:
        tool_hint = "IMPORTANT: You MUST use tools for EVERY user query..."
    # ... rest of implementation
```

### 3. Tool Configuration
```python
def _build_tool_config_for_bedrock(self, tools_available=None):
    if self.force_tool_usage:
        return {"tools": tools, "toolChoice": {"any": {}}}
    else:
        return {"tools": tools, "toolChoice": {"auto": {}}}
```

### 4. Conversation Execution
```python
async def _execute_contextual_conversation(self, ...):
    tool_calls_made = False  # Track tool usage
    
    # ... conversation loop ...
    
    if self.force_tool_usage and tool_config and not tool_calls_made:
        # Retry with enhanced prompt or add warning
```

## Benefits

1. **Fresh Data**: Ensures responses are based on current information
2. **Consistency**: Every query gets the same level of tool-assisted research
3. **Reliability**: Prevents stale or cached information from being used
4. **Transparency**: Clear indicators when fresh data wasn't retrieved

## Use Cases

### When to Enable (FORCE_TOOL_USAGE=true)
- Real-time data applications (weather, stock prices, news)
- Systems where data freshness is critical
- Applications requiring consistent tool usage patterns
- Debugging scenarios where you want to see all tool interactions

### When to Disable (FORCE_TOOL_USAGE=false)
- Conversational applications where context matters more than freshness
- Systems with expensive tool calls
- Applications where the model should decide tool usage based on query type
- Performance-sensitive scenarios

## Testing

Use the provided test script to verify the functionality:

```bash
python test_force_tools.py
```

This will demonstrate:
- System message differences between forced and optional modes
- Tool choice configuration changes
- Behavior verification

## Monitoring

The system logs tool usage patterns:

```python
logger.warning(f"Session {session_id}: Model ended without using tools despite tool_config present")
logger.info(f"Completed contextual chat for session {session_id}: {len(tools_used)} tools used")
```

Monitor these logs to understand tool usage patterns and effectiveness of the forced usage feature.

## Migration Guide

### Existing Applications

1. **No changes required** - The feature defaults to enabled
2. **To disable**: Set `FORCE_TOOL_USAGE=false` in your environment
3. **To customize**: Modify the environment variable as needed

### Performance Considerations

- Forced tool usage may increase response time due to mandatory tool calls
- Consider the cost implications of increased tool usage
- Monitor tool call frequency and adjust based on your use case

## Troubleshooting

### Model Still Not Using Tools
1. Verify `FORCE_TOOL_USAGE=true` is set
2. Check that tools are available in the tool configuration
3. Review system message content in logs
4. Ensure tool choice is set to "any" not "auto"

### Too Many Tool Calls
1. Set `FORCE_TOOL_USAGE=false` to make tools optional
2. Adjust the system message to be more specific about when tools are needed
3. Consider implementing tool usage limits if needed

### Performance Issues
1. Monitor tool execution time
2. Consider caching strategies for frequently accessed data
3. Optimize tool implementations for faster execution
4. Use `FORCE_TOOL_USAGE=false` for non-critical queries
