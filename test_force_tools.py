#!/usr/bin/env python3
"""
Test script to demonstrate forced tool usage functionality.
This script shows how the enhanced MCP manager forces tool execution on every query.
"""

import os
import asyncio
import logging
from typing import Dict, Any

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Mock classes for testing (replace with actual imports in real usage)
class MockChatSession:
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.context = f"Mock context for session {session_id}"
    
    def get_context_for_bedrock(self) -> str:
        return self.context
    
    def get_bedrock_messages(self, max_turns: int = 8):
        return []
    
    def add_turn(self, user_message: str, assistant_response: str, tools_used: list):
        logger.info(f"Added turn to session {self.session_id}: {len(tools_used)} tools used")

class MockSessionManager:
    def get_or_create_session(self, session_id: str):
        return MockChatSession(session_id)

class MockMCPClientManager:
    def get_available_tools(self) -> Dict[str, Any]:
        return {
            "weather_tool": {
                "tool": {
                    "name": "get_weather",
                    "description": "Get current weather information",
                    "input_schema": {
                        "type": "object",
                        "properties": {
                            "location": {"type": "string", "description": "Location to get weather for"}
                        },
                        "required": ["location"]
                    }
                },
                "server": "weather_server"
            },
            "time_tool": {
                "tool": {
                    "name": "get_time",
                    "description": "Get current time",
                    "input_schema": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                },
                "server": "time_server"
            }
        }
    
    async def call_tool(self, server_name: str, tool_name: str, tool_input: Dict[str, Any]):
        # Mock tool execution
        if tool_name == "get_weather":
            return {
                "success": True,
                "result": f"Weather in {tool_input.get('location', 'unknown')}: Sunny, 22°C"
            }
        elif tool_name == "get_time":
            return {
                "success": True,
                "result": "Current time: 2024-01-15 14:30:00 UTC"
            }
        else:
            return {
                "success": False,
                "error": f"Unknown tool: {tool_name}"
            }

# Import the enhanced MCP manager
try:
    from enhanced_mcp_manager import EnhancedMCPMixin
except ImportError:
    logger.error("Could not import EnhancedMCPMixin. Make sure enhanced_mcp_manager.py is in the same directory.")
    exit(1)

class TestMCPManager(EnhancedMCPMixin, MockMCPClientManager):
    """Test implementation combining the enhanced MCP mixin with mock functionality."""
    
    def __init__(self):
        super().__init__()
        self.model_id = "apac.amazon.nova-lite-v1:0"

async def test_forced_tool_usage():
    """Test the forced tool usage functionality."""
    
    # Test with FORCE_TOOL_USAGE=true (default)
    os.environ["FORCE_TOOL_USAGE"] = "true"
    
    manager = TestMCPManager()
    
    print("=== Testing Forced Tool Usage (FORCE_TOOL_USAGE=true) ===")
    print(f"Force tool usage enabled: {manager.force_tool_usage}")
    
    # Test system message generation
    mock_session = MockChatSession("test_session")
    tools_available = ["weather_tool", "time_tool"]
    
    system_message = manager._build_context_aware_system_message(mock_session, tools_available)
    print(f"\nSystem message with forced tools:\n{system_message}")
    
    # Test tool config generation
    tool_config = manager._build_tool_config_for_bedrock(tools_available)
    print(f"\nTool config: {tool_config}")
    
    if tool_config:
        tool_choice = tool_config.get("toolChoice", {})
        if "any" in tool_choice:
            print("✓ Tool choice set to 'any' - tools will be forced")
        elif "auto" in tool_choice:
            print("✗ Tool choice set to 'auto' - tools are optional")
        else:
            print(f"? Unknown tool choice: {tool_choice}")
    
    print("\n" + "="*60)
    
    # Test with FORCE_TOOL_USAGE=false
    os.environ["FORCE_TOOL_USAGE"] = "false"
    
    manager2 = TestMCPManager()
    
    print("=== Testing Optional Tool Usage (FORCE_TOOL_USAGE=false) ===")
    print(f"Force tool usage enabled: {manager2.force_tool_usage}")
    
    system_message2 = manager2._build_context_aware_system_message(mock_session, tools_available)
    print(f"\nSystem message with optional tools:\n{system_message2}")
    
    tool_config2 = manager2._build_tool_config_for_bedrock(tools_available)
    print(f"\nTool config: {tool_config2}")
    
    if tool_config2:
        tool_choice2 = tool_config2.get("toolChoice", {})
        if "any" in tool_choice2:
            print("✗ Tool choice set to 'any' - tools will be forced")
        elif "auto" in tool_choice2:
            print("✓ Tool choice set to 'auto' - tools are optional")
        else:
            print(f"? Unknown tool choice: {tool_choice2}")

def main():
    """Main function to run the test."""
    print("Testing Enhanced MCP Manager - Forced Tool Usage")
    print("=" * 60)
    
    try:
        asyncio.run(test_forced_tool_usage())
        print("\n✓ Test completed successfully!")
        print("\nTo enable forced tool usage in your application:")
        print("1. Set environment variable: FORCE_TOOL_USAGE=true")
        print("2. The system will automatically force tool calls on every query")
        print("3. Set FORCE_TOOL_USAGE=false to disable forced tool usage")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        print(f"\n✗ Test failed: {e}")

if __name__ == "__main__":
    main()
