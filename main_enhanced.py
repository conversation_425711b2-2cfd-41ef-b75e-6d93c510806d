"""
Enhanced FastAPI Backend for MCP Client with Context Retention and Session Discovery - BEDROCK ONLY

Extends the existing main.py with session-aware conversation management and session discovery
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import asyncio
import logging
from contextlib import asynccontextmanager
import uvicorn
import os
import uuid

from main import (
    MCPServerConfig, ChatRequest,
    MCPServerConnection, MCPClientManager, DEFAULT_MCP_SERVERS
)

# Import Bedrock session management
from session_manager_new import session_manager
from enhanced_mcp_manager import EnhancedMCPMixin

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedMCPClientManager(EnhancedMCPMixin, MCPClientManager):
    """
    Enhanced MCP Client Manager with context retention capabilities.
    """

    def __init__(self):
        super().__init__()
        self.model_id = os.getenv("BEDROCK_MODEL_ID", "apac.amazon.nova-lite-v1:0")
        self._async_bedrock_client = None
        logger.info(f"Enhanced MCP Client Manager initialized with context retention and force_tool_usage={self.force_tool_usage}")

    async def get_async_bedrock_runtime(self):
        """Provide a pooled async bedrock-runtime client."""
        try:
            import aioboto3
        except ImportError as e:
            raise ImportError("aioboto3 is required for async Bedrock operations. pip install aioboto3") from e
        
        if self._async_bedrock_client is None:
            session = aioboto3.Session()
            self._async_bedrock_client = await session.client("bedrock-runtime").__aenter__()
        
        return self._async_bedrock_client

class EnhancedChatResponse(BaseModel):
    response: str
    conversation_id: str
    tools_used: List[Dict[str, Any]] = []
    status: str = "success"
    session_stats: Optional[Dict[str, Any]] = None
    context_used: bool = False

enhanced_mcp_manager = EnhancedMCPClientManager()

async def session_monitor_worker():
    """Background worker to monitor Bedrock session health."""
    try:
        while True:
            try:
                stats = session_manager.get_all_sessions_stats()
                active_count = stats.get("active_sessions", 0)
                if active_count > 0:
                    logger.info(f"Active Bedrock sessions: {active_count}")
                await asyncio.sleep(300)  # Check every 5 minutes
            except asyncio.CancelledError:
                logger.info("Session monitor worker cancelled")
                break
            except Exception as e:
                logger.error(f"Session monitor error: {e}")
                await asyncio.sleep(60)
    except asyncio.CancelledError:
        logger.info("Session monitor worker cancelled")
    except Exception as e:
        logger.error(f"Session monitor worker error: {e}")

async def configure_servers_background():
    """Background task to configure MCP servers after startup."""
    logger.info("Starting background MCP server configuration...")

    async def configure_server_safe(server_config: MCPServerConfig):
        if not server_config.enabled:
            return False, f"Server {server_config.name} is disabled"

        logger.info(f"Adding server: {server_config.name}")
        max_retries = 2 if server_config.name in ["cost-explorer", "aws-pricing"] else 1

        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    logger.info(f"Retry attempt {attempt + 1} for {server_config.name}")
                    await asyncio.sleep(1.0)

                success = await enhanced_mcp_manager.add_server(server_config)
                if success:
                    logger.info(f"✅ Successfully configured {server_config.name}")
                    return True, None
                else:
                    connection = enhanced_mcp_manager.connections.get(server_config.name)
                    error_msg = connection.error if connection else "Unknown error"
                    if "timeout" not in error_msg.lower() or attempt == max_retries - 1:
                        logger.error(f"❌ Failed to configure {server_config.name}: {error_msg}")
                        return False, error_msg
                    else:
                        logger.warning(f"⚠️ Timeout for {server_config.name}, will retry...")
            except asyncio.CancelledError:
                logger.info(f"Configuration cancelled for {server_config.name}")
                return False, "Cancelled"
            except Exception as e:
                if attempt == max_retries - 1:
                    logger.error(f"❌ Error configuring {server_config.name}: {e}")
                    return False, str(e)
                else:
                    logger.warning(f"⚠️ Exception for {server_config.name}, will retry: {e}")

        return False, "Max retries exceeded"

    try:
        results = []
        for config in DEFAULT_MCP_SERVERS:
            logger.info(f"Configuring server: {config.name}")
            try:
                result = await configure_server_safe(config)
                results.append(result)
            except Exception as e:
                logger.error(f"❌ Failed to configure {config.name}: {e}")
                results.append((False, str(e)))
            await asyncio.sleep(0.5)

        # Count only successful tuples (True, None)
        successful_count = sum(1 for res in results if isinstance(res, tuple) and res[0])
        logger.info(f"🚀 Background server configuration complete: {successful_count}/{len(DEFAULT_MCP_SERVERS)} servers connected")

        if successful_count == 0:
            logger.warning("⚠️ No MCP servers connected")
        else:
            logger.info(f"✅ Successfully connected servers: {successful_count}")

    except asyncio.CancelledError:
        logger.info("Background server configuration cancelled")
    except Exception as e:
        logger.error(f"Error in background server configuration: {e}")

@asynccontextmanager
async def enhanced_lifespan(app: FastAPI):
    """Enhanced lifespan with BEDROCK-ONLY session validation and optional startup discovery."""
    logger.info("Starting Enhanced MCP Client API with BEDROCK-ONLY Context Retention and Session Discovery")

    # 🔥 VALIDATE BEDROCK AVAILABILITY AT STARTUP
    try:
        session_manager.get_all_sessions_stats()
        if not session_manager.backend:
            raise RuntimeError("Bedrock-only mode configured but no backend available")
        logger.info("✅ Bedrock-only session management validated successfully")

        # 🚀 OPTIONAL: Pre-discover sessions at startup (comment out if too slow)
        startup_discovery = os.getenv("STARTUP_DISCOVERY", "false").lower() == "true"
        if startup_discovery:
            try:
                logger.info("🔍 Pre-discovering sessions at startup...")
                discovered = session_manager.discover_all_sessions()
                logger.info(f"📊 Startup discovery found {len(discovered)} sessions")
            except Exception as e:
                logger.warning(f"⚠️ Startup session discovery failed (non-critical): {e}")

    except Exception as e:
        logger.error(f"❌ STARTUP FAILED: Bedrock-only session management validation failed: {e}")
        logger.error("Check: AWS credentials, region (ap-south-1), and Bedrock Agent Runtime access")
        raise

    monitor_task = asyncio.create_task(session_monitor_worker())

    # Start server configuration in background to avoid blocking startup
    config_task = None
    if os.getenv("AUTO_CONFIGURE_SERVERS", "true").lower() == "true":
        logger.info("Starting background MCP server configuration...")
        config_task = asyncio.create_task(configure_servers_background())

    try:
        yield
    finally:
        logger.info("Shutting down Enhanced MCP Client API")

        # Cancel background tasks
        if config_task and not config_task.done():
            config_task.cancel()
            try:
                await config_task
            except asyncio.CancelledError:
                pass

        monitor_task.cancel()
        try:
            await monitor_task
        except asyncio.CancelledError:
            pass

        try:
            await enhanced_mcp_manager.cleanup()
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

app = FastAPI(
    title="Enhanced MCP Client API with Bedrock-Only Context Retention and Session Discovery",
    description="Multi-server MCP client with Bedrock integration, conversation context, and session discovery",
    version="2.1.0",
    lifespan=enhanced_lifespan
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Health check endpoint with session stats."""
    session_stats = session_manager.get_all_sessions_stats()
    return {
        "message": "Enhanced MCP Client API with Bedrock-Only Context Retention and Session Discovery is running",
        "status": "healthy",
        "mode": "bedrock_only",
        "features": ["context_retention", "session_management", "tool_tracking", "bedrock_persistence", "session_discovery"],
        "session_stats": session_stats
    }

@app.post("/chat", response_model=EnhancedChatResponse)
async def enhanced_chat_endpoint(request: ChatRequest):
    """Enhanced chat endpoint with Bedrock-only context retention."""
    try:
        conversation_id = request.conversation_id or f"conv_{uuid.uuid4().hex[:8]}"
        tools_available = list(enhanced_mcp_manager.get_available_tools().keys()) if request.use_tools else []
        
        result = await enhanced_mcp_manager.chat_with_bedrock_with_context(
            message=request.message,
            session_id=conversation_id,
            tools_available=tools_available
        )
        
        chat_session = session_manager.get_session(conversation_id)
        session_stats = chat_session.get_session_stats() if chat_session else {"error": "Session not found"}
        
        return EnhancedChatResponse(
            response=result["response"],
            conversation_id=conversation_id,
            tools_used=result["tools_used"],
            status="success" if not result.get("error") else "error",
            session_stats=session_stats,
            context_used=session_stats.get('total_turns', 0) > 0 if session_stats and 'error' not in session_stats else False
        )
    except Exception as e:
        logger.error(f"Enhanced chat error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Session Discovery and Recovery endpoints
@app.get("/sessions/discover")
async def discover_sessions(search_term: str = "mcp-bot"):
    """Discover all available sessions from Bedrock."""
    try:
        discovered_sessions = session_manager.discover_sessions(search_term)
        
        return {
            "discovered_sessions": discovered_sessions,
            "total_count": len(discovered_sessions),
            "source": "bedrock_native"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Discovery failed: {str(e)}")

@app.get("/sessions/discover/all")
async def discover_all_sessions():
    """Discover all available sessions with detailed metadata."""
    try:
        sessions = session_manager.discover_all_sessions()
        return {
            "sessions": sessions,
            "total_count": len(sessions),
            "source": "bedrock_comprehensive"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Discovery failed: {str(e)}")

@app.post("/sessions/restore/{session_id}")
async def restore_session(session_id: str):
    """Restore a session from Bedrock."""
    try:
        restored_session = session_manager.restore_session_by_id(session_id)
        
        if not restored_session:
            raise HTTPException(status_code=404, detail="Session not found or could not be restored")
        
        stats = restored_session.get_session_stats()
        
        return {
            "message": f"Session {session_id} restored successfully",
            "session_stats": stats,
            "conversation_turns": len(restored_session.conversation_history),
            "bedrock_session_id": stats.get("bedrock_session_id")
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Restore failed: {str(e)}")

@app.post("/sessions/restore/{session_id}/continue")
async def restore_and_continue_session(session_id: str):
    """Restore session and prepare for continuation with full conversation history."""
    try:
        result = session_manager.continue_conversation(session_id)

        if not result["success"]:
            raise HTTPException(status_code=404, detail=result["error"])

        # Get the actual session to extract conversation history
        session = session_manager.sessions.get(session_id)
        history = []

        if session and hasattr(session, 'conversation_history'):
            for turn in session.conversation_history:
                # Use the ConversationTurn's to_dict() method
                if hasattr(turn, 'to_dict'):
                    history.append(turn.to_dict())
                else:
                    # Fallback for dict-like objects
                    history.append({
                        "user_message": getattr(turn, 'user_message', ''),
                        "assistant_response": getattr(turn, 'assistant_response', ''),
                        "timestamp": getattr(turn, 'timestamp', ''),
                        "tools_used": getattr(turn, 'tools_used', [])
                    })

        return jsonable_encoder({
            "success": True,
            "message": f"Session {session_id} restored and ready for continuation",
            "session_restored": result["session_restored"],
            "conversation_turns": result["conversation_turns"],
            "context_summary": result["context_summary"],
            "last_activity": result["last_activity"],
            "total_tools_used": result["total_tools_used"],
            "history": history  # Add the conversation history for frontend
        })
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Restoration failed: {str(e)}")

@app.get("/sessions/bedrock/list")
async def list_bedrock_sessions():
    """List all Bedrock sessions directly."""
    try:
        if not session_manager.backend:
            raise HTTPException(status_code=503, detail="Bedrock backend not available")
        
        bedrock_sessions = session_manager.backend.list_all_sessions()
        
        return {
            "bedrock_sessions": bedrock_sessions,
            "total_count": len(bedrock_sessions),
            "note": "These are all sessions in your Bedrock account"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list Bedrock sessions: {str(e)}")

@app.get("/sessions/{session_id}/preview")
async def get_session_preview(session_id: str):
    """Get a quick preview of session without full restoration."""
    try:
        preview = session_manager._get_conversation_preview(session_id)
        return {
            "session_id": session_id,
            "preview": preview
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Preview failed: {str(e)}")

@app.get("/sessions/list-with-titles")
async def list_sessions_with_titles():
    """List all sessions with titles and previews - PRODUCTION VERSION with real discovery"""
    try:
        sessions_data = []

        # Get all active sessions (in-memory)
        for session_id, session in session_manager.sessions.items():
            try:
                session_summary = session.get_session_summary()
                # Convert datetime to string before adding
                if 'last_activity' in session_summary and hasattr(session_summary['last_activity'], 'isoformat'):
                    session_summary['last_activity'] = session_summary['last_activity'].isoformat()
                sessions_data.append(session_summary)
            except Exception as e:
                logger.error(f"Error getting summary for active session {session_id}: {e}")
                continue

        # ✅ ENABLE REAL SESSION DISCOVERY FROM BEDROCK
        if session_manager.backend:
            try:
                logger.info("🔍 Discovering sessions from Bedrock...")
                discovered_sessions = session_manager.discover_all_sessions()
                logger.info(f"📊 Found {len(discovered_sessions)} sessions in Bedrock")

                # Add discovered sessions that aren't already in memory
                in_memory_ids = {s.get("session_id") for s in sessions_data}
                for discovered in discovered_sessions:
                    if discovered.get("session_id") not in in_memory_ids:
                        sessions_data.append(discovered)

            except Exception as e:
                logger.error(f"❌ Session discovery failed: {e}")
                # Don't fail completely, just log the error

        # Remove duplicates by session_id
        unique_sessions = {}
        for session in sessions_data:
            session_id = session.get("session_id") or session.get("bedrock_session_id")
            if session_id:
                unique_sessions[session_id] = session

        # Sort by last activity (most recent first)
        sorted_sessions = sorted(
            unique_sessions.values(),
            key=lambda x: x.get("last_activity", ""),
            reverse=True
        )

        # ❌ REMOVE ALL MOCK SESSION CODE
        # Delete the entire mock_sessions block and the conditional logic

        result = {
            "sessions": sorted_sessions,
            "count": len(sorted_sessions),
            "success": True,
            "source": "bedrock_discovery_enabled"
        }
        return jsonable_encoder(result)

    except Exception as e:
        logger.error(f"Failed to list sessions with titles: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve sessions: {str(e)}")

@app.post("/sessions/{session_id}/rename")
async def rename_session(session_id: str, request: dict):
    """Rename a session (store in metadata)."""
    try:
        new_title = request.get("new_title", "")
        if not new_title:
            raise HTTPException(status_code=400, detail="new_title is required")

        session = session_manager.get_session(session_id)
        if session:
            # Store custom title in session metadata or context
            session.custom_title = new_title
            return {"success": True, "message": f"Session renamed to '{new_title}'"}
        return {"success": False, "message": "Session not found"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/sessions/{session_id}/debug")
async def debug_session(session_id: str):
    """Debug a session by inspecting raw Bedrock data."""
    try:
        # Get or create session to access debug method
        chat_session = session_manager.get_session(session_id)
        if not chat_session:
            # Try to restore session first
            chat_session = session_manager.restore_session_by_id(session_id)
            if not chat_session:
                raise HTTPException(status_code=404, detail="Session not found")

        # Call debug method
        chat_session.debug_bedrock_session(session_id)

        return {
            "message": f"Debug information for session {session_id} logged",
            "session_id": session_id,
            "conversation_turns": len(chat_session.conversation_history),
            "note": "Check server logs for detailed debug information"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Debug failed: {str(e)}")

@app.get("/sessions/{session_id}/history")
async def get_session_history(session_id: str):
    """Get conversation history for a session."""
    chat_session = session_manager.get_session(session_id)
    if not chat_session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    session_stats = chat_session.get_session_stats()
    history = [turn.to_dict() for turn in chat_session.conversation_history]
    
    return {
        "session_id": session_id,
        "created_at": session_stats.get('created_at'),
        "last_activity": session_stats.get('last_activity'),
        "message_count": len(history),
        "total_tools_used": session_stats.get('total_tools_used', 0),
        "history": history
    }

@app.get("/sessions/{session_id}/stats")
async def get_session_stats(session_id: str):
    """Get detailed statistics for a session."""
    chat_session = session_manager.get_session(session_id)
    if not chat_session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    return chat_session.get_session_stats()

@app.delete("/sessions/{session_id}")
async def clear_session(session_id: str):
    """Delete a Bedrock session."""
    try:
        success = session_manager.delete_session(session_id)
        if success:
            return {"message": f"Session {session_id} deleted successfully"}
        else:
            raise HTTPException(status_code=404, detail="Session not found")
    except Exception as e:
        logger.error(f"Error deleting session: {e}")
        raise HTTPException(status_code=404, detail="Session not found or could not be deleted")

@app.get("/sessions")
async def list_sessions():
    """List all active sessions with basic stats."""
    sessions_info = {}
    for sid, chat_session in session_manager.sessions.items():
        try:
            session_stats = chat_session.get_session_stats()
            sessions_info[sid] = session_stats
        except Exception as e:
            logger.warning(f"Could not get stats for session {sid}: {e}")
    
    return {
        "sessions": sessions_info,
        "total_sessions": len(sessions_info),
        "global_stats": session_manager.get_all_sessions_stats()
    }

@app.post("/sessions/cleanup")
async def manual_session_cleanup():
    """Manually trigger session cleanup."""
    cleaned_count = session_manager.cleanup_expired_sessions()
    return {
        "message": f"Cleaned up {cleaned_count} expired sessions",
        "remaining_sessions": len(session_manager.sessions),
        "global_stats": session_manager.get_all_sessions_stats()
    }

# Server management endpoints
@app.get("/servers")
async def list_servers():
    """List all configured MCP servers."""
    servers_info = {}
    for name, connection in enhanced_mcp_manager.connections.items():
        servers_info[name] = {
            "name": name,
            "status": connection.status,
            "tools_count": len(connection.tools),
            "resources_count": len(connection.resources),
            "description": connection.config.description,
            "enabled": connection.config.enabled,
            "error": connection.error
        }
    return servers_info

@app.post("/servers")
async def add_server(config: MCPServerConfig):
    """Add a new MCP server."""
    success = await enhanced_mcp_manager.add_server(config)
    if success:
        return {"message": f"Server {config.name} added successfully"}
    else:
        connection = enhanced_mcp_manager.connections.get(config.name)
        error_msg = connection.error if connection else "Unknown error"
        raise HTTPException(status_code=400, detail=f"Failed to add server {config.name}: {error_msg}")

@app.get("/tools")
async def list_tools():
    """List all available tools across all servers."""
    tools = {}
    available_tools = enhanced_mcp_manager.get_available_tools()
    for tool_key, tool_data in available_tools.items():
        tools[tool_key] = {
            "server": tool_data["server"],
            "name": tool_data["tool"]["name"],
            "description": tool_data["tool"]["description"],
            "input_schema": tool_data["tool"].get("input_schema", {})
        }
    return tools

@app.post("/tools/call")
async def call_tool_endpoint(server_name: str, tool_name: str, arguments: Dict[str, Any]):
    """Call a specific tool."""
    result = await enhanced_mcp_manager.call_tool(server_name, tool_name, arguments)
    if result["success"]:
        return result
    else:
        raise HTTPException(status_code=400, detail=result["error"])

@app.get("/bedrock/validate")
async def validate_bedrock_setup():
    """Validate Bedrock-only configuration."""
    try:
        # Test session manager
        if not session_manager.backend:
            return {"status": "error", "message": "No Bedrock backend available"}

        # Test session creation (and clean up)
        test_session = session_manager.get_or_create_session("test_validation")
        session_stats = test_session.get_session_stats()
        session_manager.delete_session("test_validation")

        return {
            "status": "success",
            "message": "Bedrock-only configuration validated",
            "backend_available": True,
            "session_creation": "success",
            "bedrock_session_id": session_stats.get("bedrock_session_id"),
            "region": session_manager.backend.region_name if session_manager.backend else None
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Bedrock validation failed: {str(e)}",
            "backend_available": bool(session_manager.backend)
        }

@app.get("/debug/discover-sessions")
async def debug_discover_sessions():
    """Debug endpoint to test session discovery"""
    try:
        if not session_manager.backend:
            return {"error": "No Bedrock backend available"}

        logger.info("🔍 Manual session discovery triggered")

        # Test basic session listing
        all_sessions = session_manager.backend.list_all_sessions(max_results=10)
        logger.info(f"Raw sessions from Bedrock: {len(all_sessions)}")

        # Test full discovery
        discovered = session_manager.discover_all_sessions()
        logger.info(f"Processed sessions: {len(discovered)}")

        return {
            "raw_sessions_count": len(all_sessions),
            "processed_sessions_count": len(discovered),
            "raw_sessions": [s.get("sessionId") for s in all_sessions[:5]],  # First 5 IDs
            "processed_sessions": discovered[:3],  # First 3 full objects
            "backend_region": session_manager.backend.region_name if hasattr(session_manager.backend, 'region_name') else "unknown"
        }

    except Exception as e:
        logger.error(f"Debug discovery failed: {e}")
        return {"error": str(e), "type": type(e).__name__}

@app.post("/debug/logging/{level}")
async def set_logging_level(level: str):
    """Set logging level for debugging."""
    try:
        level_upper = level.upper()
        if level_upper not in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
            raise HTTPException(status_code=400, detail="Invalid logging level")

        # Set logging level
        logging.getLogger().setLevel(getattr(logging, level_upper))
        logger.setLevel(getattr(logging, level_upper))

        # Also set for session manager logger if it exists
        session_logger = logging.getLogger("session_manager_new")
        session_logger.setLevel(getattr(logging, level_upper))

        return {
            "message": f"Logging level set to {level_upper}",
            "level": level_upper,
            "note": "Use DEBUG level to see detailed conversation history recovery logs"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to set logging level: {str(e)}")

@app.get("/debug/session-discovery-detailed")
async def debug_session_discovery_detailed():
    """Comprehensive session discovery debugging"""
    try:
        if not session_manager.backend:
            return {"error": "No Bedrock backend available"}

        logger.info("🔍 Starting detailed session discovery debug")

        # Step 1: List all sessions
        raw_sessions = session_manager.backend.list_all_sessions(max_results=10)
        logger.info(f"📋 Raw sessions: {len(raw_sessions)}")

        debug_info = {
            "raw_sessions_count": len(raw_sessions),
            "raw_sessions": [],
            "session_details": [],
            "discovery_result": None,
            "errors": []
        }

        # Step 2: Process each session in detail
        for i, session in enumerate(raw_sessions):
            session_id = session.get("sessionId")
            debug_info["raw_sessions"].append({
                "index": i,
                "sessionId": session_id,
                "createdAt": str(session.get("createdAt")),
                "lastUpdatedAt": str(session.get("lastUpdatedAt")),
                "sessionStatus": session.get("sessionStatus")
            })

            try:
                # Get detailed session info
                session_details = session_manager.backend.get_session(session_id)

                # Get invocation steps
                steps, _ = session_manager.backend.list_invocation_steps(session_id, max_steps=5)

                # Get conversation preview
                preview = session_manager._get_conversation_preview(session_id)

                debug_info["session_details"].append({
                    "sessionId": session_id,
                    "session_details_keys": list(session_details.keys()) if session_details else [],
                    "steps_count": len(steps),
                    "steps_sample": [
                        {
                            "invocationId": s.get("invocationIdentifier"),
                            "stepId": s.get("invocationStepId"),
                            "stepTime": str(s.get("invocationStepTime"))
                        } for s in steps[:2]
                    ],
                    "preview": preview
                })

            except Exception as e:
                debug_info["errors"].append({
                    "sessionId": session_id,
                    "error": str(e),
                    "error_type": type(e).__name__
                })

        # Step 3: Run full discovery
        try:
            discovered = session_manager.discover_all_sessions()
            debug_info["discovery_result"] = {
                "count": len(discovered),
                "sessions": discovered[:3]  # First 3 for brevity
            }
        except Exception as e:
            debug_info["discovery_error"] = str(e)

        return debug_info

    except Exception as e:
        logger.error(f"Debug endpoint failed: {e}")
        return {"error": str(e), "type": type(e).__name__}

@app.get("/debug/raw-session-data/{session_id}")
async def debug_raw_session_data(session_id: str):
    """Debug raw session data for a specific session"""
    try:
        if not session_manager.backend:
            return {"error": "No Bedrock backend available"}

        # Get session details
        session_details = session_manager.backend.get_session(session_id)

        # Get invocation steps
        steps, _ = session_manager.backend.list_invocation_steps(session_id, max_steps=3)

        raw_data = {
            "session_id": session_id,
            "session_details": session_details,
            "steps_count": len(steps),
            "steps": []
        }

        # Get raw payload for first step
        for step in steps[:1]:  # Just first step for debugging
            inv_id = step.get("invocationIdentifier")
            step_id = step.get("invocationStepId")

            if inv_id and step_id:
                detail = session_manager.backend.get_invocation_step(session_id, inv_id, step_id)
                if detail and "invocationStep" in detail:
                    invocation_step = detail["invocationStep"]
                    payload = invocation_step.get("payload", {})
                    raw_data["steps"].append({
                        "invocation_id": inv_id,
                        "step_id": step_id,
                        "raw_payload": payload
                    })

        return raw_data

    except Exception as e:
        return {"error": str(e), "type": type(e).__name__}

if __name__ == "__main__":
    uvicorn.run(
        "main_enhanced:app",
        host=os.getenv("API_HOST", "0.0.0.0"),
        port=int(os.getenv("API_PORT", "8000")),
        reload=True,
        log_level=os.getenv("LOG_LEVEL", "info")
    )
