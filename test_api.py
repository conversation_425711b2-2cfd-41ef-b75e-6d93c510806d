#!/usr/bin/env python3
"""
Test script to verify the API works with the fixed tool name sanitization.
"""

import requests
import json

def test_api():
    """Test the enhanced MCP API with tool calls."""
    
    # Test 1: List available tools
    print("=== Test 1: List Available Tools ===")
    try:
        response = requests.get("http://localhost:8000/tools")
        if response.status_code == 200:
            tools = response.json()
            print(f"✅ Found {len(tools)} tools")
            
            # Show a few example tools
            for i, (tool_key, tool_info) in enumerate(list(tools.items())[:5]):
                print(f"  {i+1}. {tool_key}: {tool_info['name']} ({tool_info['server']})")
        else:
            print(f"❌ Failed to list tools: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error listing tools: {e}")
        return False
    
    # Test 2: Simple chat without tools
    print("\n=== Test 2: Simple Chat (No Tools) ===")
    try:
        payload = {
            "message": "Hello, can you help me?",
            "session_id": "test-session-1"
        }
        response = requests.post("http://localhost:8000/chat", json=payload)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Chat response: {result['response'][:100]}...")
        else:
            print(f"❌ Chat failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error in chat: {e}")
        return False
    
    # Test 3: Chat with tools (this would trigger the original validation error)
    print("\n=== Test 3: Chat with Tools (Critical Test) ===")
    try:
        payload = {
            "message": "What CloudFormation resources are available?",
            "session_id": "test-session-2",
            "tools_available": ["cloudformation::list_resources"]
        }
        response = requests.post("http://localhost:8000/chat", json=payload)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Chat with tools successful!")
            print(f"Response: {result['response'][:200]}...")
            if result.get('tools_used'):
                print(f"Tools used: {len(result['tools_used'])}")
        else:
            print(f"❌ Chat with tools failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error in chat with tools: {e}")
        return False
    
    print("\n🎉 All tests passed! The Bedrock validation error has been fixed.")
    return True

if __name__ == "__main__":
    test_api()
