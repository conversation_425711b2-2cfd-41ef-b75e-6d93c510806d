"""
Enhanced MCP Manager with Bedrock Session Management

Async-first mixin aligned with Bedrock-backed session_manager integration.
- Uses ChatSession for context/history
- Builds valid Converse toolConfig (JSON schema object)
- Persists turns to Bedrock via ChatSession.add_turn (which writes invocation steps)
"""

import logging
import os
import json
from typing import Dict, List, Any, Optional
from session_manager_new import session_manager

logger = logging.getLogger(__name__)

class EnhancedMCPMixin:
    """
    Mixin class to add Bedrock session-aware functionality to existing MCPClientManager.
    Uses native Bedrock session management (via session_manager) for context retention.
    """

    # Default; manager overrides from env
    model_id: str = "apac.amazon.nova-lite-v1:0"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Force tool usage setting
        self.force_tool_usage = os.getenv("FORCE_TOOL_USAGE", "true").lower() == "true"

        # Initialize tool name mappings if not already present
        if not hasattr(self, '_tool_name_mapping'):
            self._tool_name_mapping: Dict[str, str] = {}
        if not hasattr(self, '_reverse_tool_name_mapping'):
            self._reverse_tool_name_mapping: Dict[str, str] = {}

    def _sanitize_tool_name_for_bedrock(self, original_name: str) -> str:
        """
        Sanitize tool name to comply with Bedrock's naming pattern: [a-zA-Z0-9_-]+
        Replace :: with _ and any other invalid characters with _
        """
        import re
        # Replace :: with _
        sanitized = original_name.replace("::", "_")
        # Replace any other invalid characters with _
        sanitized = re.sub(r'[^a-zA-Z0-9_-]', '_', sanitized)
        return sanitized

    def _ensure_mappings_initialized(self):
        """Ensure tool name mappings are initialized."""
        if not hasattr(self, '_tool_name_mapping'):
            self._tool_name_mapping: Dict[str, str] = {}
        if not hasattr(self, '_reverse_tool_name_mapping'):
            self._reverse_tool_name_mapping: Dict[str, str] = {}

    def _flatten_schema_for_bedrock(self, schema: Dict[str, Any]) -> Dict[str, Any]:
        """
        Flatten complex JSON schemas for Bedrock compatibility.
        Removes $defs references and simplifies nested structures.
        """
        if not isinstance(schema, dict):
            return schema

        # Create a copy to avoid modifying the original
        flattened = schema.copy()

        # Remove $defs as Bedrock doesn't support JSON Schema references
        if "$defs" in flattened:
            defs = flattened.pop("$defs")
            # Try to inline simple definitions
            flattened = self._inline_simple_refs(flattened, defs)

        # Simplify complex anyOf/oneOf structures
        if "properties" in flattened:
            flattened["properties"] = self._simplify_properties(flattened["properties"])

        # Ensure required fields are simple
        if "required" in flattened and not isinstance(flattened["required"], list):
            flattened["required"] = []

        return flattened

    def _inline_simple_refs(self, schema: Dict[str, Any], defs: Dict[str, Any]) -> Dict[str, Any]:
        """Inline simple $ref definitions into the schema."""
        if isinstance(schema, dict):
            if "$ref" in schema:
                ref_path = schema["$ref"]
                if ref_path.startswith("#/$defs/"):
                    def_name = ref_path.replace("#/$defs/", "")
                    if def_name in defs:
                        # Return the definition directly, but simplified
                        return self._simplify_definition(defs[def_name])
                return {"type": "object"}  # Fallback for unresolved refs
            else:
                # Recursively process nested objects
                result = {}
                for key, value in schema.items():
                    result[key] = self._inline_simple_refs(value, defs)
                return result
        elif isinstance(schema, list):
            return [self._inline_simple_refs(item, defs) for item in schema]
        else:
            return schema

    def _simplify_definition(self, definition: Dict[str, Any]) -> Dict[str, Any]:
        """Simplify a definition for Bedrock compatibility."""
        if not isinstance(definition, dict):
            return {"type": "string"}

        simplified = {}

        # Copy basic properties
        if "type" in definition:
            simplified["type"] = definition["type"]
        else:
            simplified["type"] = "object"

        if "description" in definition:
            simplified["description"] = definition["description"]

        if "properties" in definition:
            simplified["properties"] = self._simplify_properties(definition["properties"])

        if "required" in definition and isinstance(definition["required"], list):
            simplified["required"] = definition["required"]

        return simplified

    def _simplify_properties(self, properties: Dict[str, Any]) -> Dict[str, Any]:
        """Simplify property definitions for Bedrock compatibility."""
        simplified = {}

        for prop_name, prop_def in properties.items():
            if isinstance(prop_def, dict):
                if "anyOf" in prop_def:
                    # Simplify anyOf to the first non-null type
                    simplified[prop_name] = self._simplify_any_of(prop_def["anyOf"])
                elif "$ref" in prop_def:
                    # Convert refs to simple types
                    simplified[prop_name] = {"type": "string", "description": prop_def.get("description", "")}
                else:
                    # Copy the property as-is but ensure it has a type
                    simplified[prop_name] = prop_def.copy()
                    if "type" not in simplified[prop_name]:
                        simplified[prop_name]["type"] = "string"
            else:
                simplified[prop_name] = {"type": "string"}

        return simplified

    def _simplify_any_of(self, any_of_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Simplify anyOf structures to a single type."""
        # Find the first non-null type
        for option in any_of_list:
            if isinstance(option, dict) and option.get("type") != "null":
                return {
                    "type": option.get("type", "string"),
                    "description": option.get("description", "")
                }

        # Fallback to string type
        return {"type": "string"}

    def _tools_are_bedrock_compatible(self, tools: List[Dict[str, Any]]) -> bool:
        """Check if tools have schemas that are compatible with Bedrock forced tool usage."""
        try:
            for tool in tools:
                tool_spec = tool.get("toolSpec", {})
                input_schema = tool_spec.get("inputSchema", {}).get("json", {})

                # Check for overly complex schemas that might cause issues
                if self._schema_is_too_complex(input_schema):
                    logger.warning(f"Tool {tool_spec.get('name', 'unknown')} has complex schema, falling back to auto tool choice")
                    return False

            return True
        except Exception as e:
            logger.warning(f"Error checking tool compatibility: {e}, falling back to auto tool choice")
            return False

    def _schema_is_too_complex(self, schema: Dict[str, Any]) -> bool:
        """Check if a schema is too complex for reliable forced tool usage."""
        if not isinstance(schema, dict):
            return False

        # Check for indicators of complexity
        complexity_indicators = [
            "$defs" in schema,
            "$ref" in str(schema),
            len(str(schema)) > 5000,  # Very large schemas
            self._has_deep_nesting(schema, max_depth=4)
        ]

        return any(complexity_indicators)

    def _has_deep_nesting(self, obj: Any, current_depth: int = 0, max_depth: int = 4) -> bool:
        """Check if an object has deep nesting beyond the specified depth."""
        if current_depth > max_depth:
            return True

        if isinstance(obj, dict):
            return any(self._has_deep_nesting(value, current_depth + 1, max_depth) for value in obj.values())
        elif isinstance(obj, list):
            return any(self._has_deep_nesting(item, current_depth + 1, max_depth) for item in obj)
        else:
            return False

    def _update_tool_name_mappings(self, tools_available: List[str]):
        """Update the tool name mappings for the given tools."""
        self._ensure_mappings_initialized()
        available_tools = self.get_available_tools()

        for tool_key in tools_available:
            if tool_key not in available_tools:
                continue

            tool_data = available_tools[tool_key]
            original_name = tool_data["tool"]["name"]
            sanitized_name = self._sanitize_tool_name_for_bedrock(original_name)

            self._tool_name_mapping[sanitized_name] = original_name
            self._reverse_tool_name_mapping[original_name] = sanitized_name
    
    async def chat_with_bedrock_with_context(
        self,
        message: str,
        session_id: str,
        tools_available: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Enhanced Bedrock chat with native session context retention using session_manager.
        """
        try:
            chat_session = session_manager.get_or_create_session(session_id)
            historical_messages = chat_session.get_bedrock_messages(max_turns=8)
            
            current_messages = historical_messages + [{
                "role": "user",
                "content": [{"text": message}]
            }]
            
            system_message = self._build_context_aware_system_message(chat_session, tools_available)
            tool_config = self._build_tool_config_for_bedrock(tools_available)
            
            result = await self._execute_contextual_conversation(
                messages=current_messages,
                system_message=system_message,
                tool_config=tool_config,
                session_id=session_id,
                model_id=self.model_id,
            )
            
            chat_session.add_turn(
                user_message=message,
                assistant_response=result["response"],
                tools_used=result.get("tools_used", []),
            )
            
            logger.info(f"Completed contextual chat for session {session_id}: {len(result.get('tools_used', []))} tools used")
            
            return {
                "response": result["response"],
                "tools_used": result.get("tools_used", []),
                "session_id": session_id
            }
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Error in contextual chat for session {session_id}: {error_msg}")
            
            # Enhanced error handling
            if "ValidationException" in error_msg:
                if "model" in error_msg.lower():
                    response_text = f"Model validation error. Please check your BEDROCK_MODEL_ID in .env file. Current model: {self.model_id}"
                elif "region" in error_msg.lower():
                    response_text = f"Region validation error. Please check your AWS_REGION configuration. Current region: {os.getenv('AWS_REGION', 'not set')}"
                else:
                    response_text = f"Bedrock API validation error: {error_msg}"
            elif "RetryError" in error_msg:
                response_text = "Connection retry failed. Please check AWS credentials and region configuration."
            elif "AccessDenied" in error_msg:
                response_text = "Access denied. Please check your AWS permissions for Bedrock services."
            elif "ResourceNotFound" in error_msg:
                response_text = f"Resource not found. Please verify your model ID: {self.model_id}"
            else:
                response_text = f"I apologize, but I encountered an error: {error_msg}"
            
            return {
                "response": response_text,
                "tools_used": [],
                "session_id": session_id,
                "error": True
            }

    def _build_context_aware_system_message(
        self,
        chat_session,
        tools_available: Optional[List[str]] = None
    ) -> str:
        """Build context-aware system message with mandatory tool usage."""
        context = ""
        if hasattr(chat_session, "get_context_for_bedrock") and callable(chat_session.get_context_for_bedrock):
            context = chat_session.get_context_for_bedrock()

        tool_hint = ""
        if tools_available:
            if self.force_tool_usage:
                tool_hint = (
                    "\nIMPORTANT: You MUST use tools for EVERY user query to fetch fresh, up-to-date information. "
                    "Do NOT rely on previous conversation context or cached data. "
                    "Always call appropriate tools first before providing any answer, even for follow-up questions. "
                    "Treat each query as requiring fresh data retrieval. "
                    "Use tools iteratively and comprehensively to gather the most current information available."
                )
            else:
                tool_hint = (
                    "\nYou have access to tools and should use them iteratively when needed. "
                    "You can batch independent tool calls in parallel. "
                    "Continue using tools until you have all the information needed for a complete answer. "
                    "Only stop when you are confident you can provide a final, comprehensive response."
                )

        system = (
            "You are an assistant inside an MCP Bot. "
            "You must always use available tools to fetch fresh data for every user request. "
            "Never assume information from previous context is current or sufficient."
            f"\n\nSession Context (for reference only, always fetch fresh data):\n{context}"
            f"{tool_hint}"
        )

        return system

    def _build_tool_config_for_bedrock(self, tools_available: Optional[List[str]] = None) -> Optional[Dict]:
        """Build tool configuration for Bedrock with sanitized tool names."""
        if not tools_available:
            return None

        # Update tool name mappings for the current set of tools
        self._update_tool_name_mappings(tools_available)

        available_tools = self.get_available_tools()
        tools: List[Dict[str, Any]] = []

        for tool_key in tools_available:
            if tool_key not in available_tools:
                continue

            tool_data = available_tools[tool_key]
            tool = tool_data["tool"]
            original_name = tool["name"]

            # Use sanitized name for Bedrock
            sanitized_name = self._sanitize_tool_name_for_bedrock(original_name)

            input_schema = tool.get("input_schema") or {"type": "object", "properties": {}, "required": []}

            # Flatten complex schemas for Bedrock compatibility
            input_schema = self._flatten_schema_for_bedrock(input_schema)

            if "type" not in input_schema:
                input_schema["type"] = "object"
            if "properties" not in input_schema:
                input_schema["properties"] = {}

            tools.append({
                "toolSpec": {
                    "name": sanitized_name,
                    "description": tool.get("description") or f"Tool from server {tool_data['server']}",
                    "inputSchema": {"json": input_schema}
                }
            })

        if not tools:
            return None

        # Force tool usage with "any" choice instead of "auto" when enabled
        # But fall back to "auto" if tools have complex schemas that might cause issues
        if self.force_tool_usage:
            if self._tools_are_bedrock_compatible(tools):
                logger.info(f"Using forced tool choice 'any' for {len(tools)} compatible tools")
                return {"tools": tools, "toolChoice": {"any": {}}}
            else:
                logger.warning(f"Complex tool schemas detected, falling back to 'auto' tool choice for {len(tools)} tools")
                return {"tools": tools, "toolChoice": {"auto": {}}}
        else:
            logger.info(f"Using 'auto' tool choice for {len(tools)} tools (forced tool usage disabled)")
            return {"tools": tools, "toolChoice": {"auto": {}}}

    async def _execute_contextual_conversation(
        self,
        messages: List[Dict[str, Any]],
        system_message: str,
        tool_config: Optional[Dict],
        session_id: str,
        model_id: str,
    ) -> Dict[str, Any]:
        """Execute contextual conversation with mandatory tool usage."""
        runtime = await self.get_async_bedrock_runtime()
        inference_config = {"temperature": 0.4, "topP": 0.9}

        msgs = list(messages)
        tools_used: List[Dict[str, Any]] = []
        max_iterations = 15
        tool_calls_made = False  # Track if any tools were called
        
        for iteration in range(max_iterations):
            try:
                req = {
                    "modelId": model_id,
                    "messages": msgs,
                    "system": [{"text": system_message}],
                    "inferenceConfig": inference_config,
                }
                
                if tool_config:
                    req["toolConfig"] = tool_config
                
                resp = await runtime.converse(**req)
                output = resp.get("output", {}).get("message", {})
                content = output.get("content", [])
                stop_reason = resp.get("stopReason")
                
                logger.debug(f"Iteration {iteration + 1}: stop_reason={stop_reason}")
                
                if stop_reason == "tool_use":
                    tool_calls_made = True
                    # Record assistant toolUse
                    msgs.append({"role": "assistant", "content": content})

                    # Execute tool calls in parallel
                    planned_calls = []
                    for b in content:
                        if "toolUse" in b:
                            sanitized_name = b["toolUse"]["name"]
                            # Map sanitized name back to original name
                            self._ensure_mappings_initialized()
                            original_name = self._tool_name_mapping.get(sanitized_name, sanitized_name)
                            planned_calls.append({
                                "name": original_name,
                                "input": b["toolUse"].get("input", {}),
                                "toolUseId": b["toolUse"].get("toolUseId"),
                            })

                    exec_results = await self._execute_tool_calls(planned_calls, session_id)
                    tools_used.extend(exec_results)

                    # Provide toolResult blocks back to model as JSON
                    tool_result_blocks = []
                    for call, res in zip(planned_calls, exec_results):
                        payload = {"success": res.get("success", False)}

                        if res.get("success"):
                            result_data = res.get("result", "")
                            try:
                                parsed = json.loads(result_data) if isinstance(result_data, str) else result_data
                                payload["result"] = parsed
                            except Exception:
                                payload["result"] = result_data
                        else:
                            payload["error"] = res.get("error", "Unknown error")

                        tool_result_blocks.append({
                            "toolResult": {
                                "toolUseId": call["toolUseId"],
                                "content": [{"json": payload}]
                            }
                        })

                    msgs.append({"role": "user", "content": tool_result_blocks})
                    continue
                
                elif stop_reason in ("end_turn", "stop_sequence", "max_tokens"):
                    # Check if tools were available but not used when force_tool_usage is enabled
                    if self.force_tool_usage and tool_config and not tool_calls_made:
                        logger.warning(f"Session {session_id}: Model ended without using tools despite tool_config present")
                        # Force a tool call by modifying the system message and retrying once
                        if iteration == 0:  # Only retry on first iteration
                            enhanced_system = system_message + "\n\nYou MUST call at least one tool before responding. Do not provide an answer without using tools first."
                            req["system"] = [{"text": enhanced_system}]
                            # Retry with enhanced prompt
                            continue

                    final_text_parts = [b["text"] for b in content if "text" in b]
                    final_text = "\n".join(final_text_parts).strip() if final_text_parts else ""

                    # If no tools were used and tools were available with force_tool_usage, add a warning
                    if self.force_tool_usage and tool_config and not tool_calls_made:
                        final_text = f"[Note: Response generated without fresh data retrieval]\n\n{final_text}"

                    # Filter out thinking content before returning
                    filtered_text = self._filter_thinking_content(final_text)
                    return {"response": filtered_text, "tools_used": tools_used, "session_id": session_id}

                else:
                    final_text_parts = [b["text"] for b in content if "text" in b]
                    final_text = "\n".join(final_text_parts).strip() if final_text_parts else f"Response completed with stop reason: {stop_reason}"
                    # Filter out thinking content before returning
                    filtered_text = self._filter_thinking_content(final_text)
                    return {"response": filtered_text, "tools_used": tools_used, "session_id": session_id}
                    
            except Exception as e:
                error_msg = str(e)
                logger.error(f"Error in conversation iteration {iteration + 1}: {error_msg}")

                # Check if it's a tool use error and we can fall back
                if "ModelErrorException" in error_msg and "ToolUse" in error_msg and self.force_tool_usage:
                    logger.warning(f"Tool use error detected, disabling forced tool usage for this session")
                    # Temporarily disable forced tool usage and retry
                    original_force_setting = self.force_tool_usage
                    self.force_tool_usage = False

                    try:
                        # Rebuild tool config without forced usage
                        tool_config_fallback = self._build_tool_config_for_bedrock(tools_available) if tool_config else None
                        req["toolConfig"] = tool_config_fallback

                        # Retry the request
                        resp = await runtime.converse(**req)
                        output = resp.get("output", {}).get("message", {})
                        content = output.get("content", [])

                        final_text_parts = [b["text"] for b in content if "text" in b]
                        final_text = "\n".join(final_text_parts).strip() if final_text_parts else ""
                        final_text = f"[Note: Responded without forced tool usage due to compatibility issues]\n\n{final_text}"

                        # Restore original setting
                        self.force_tool_usage = original_force_setting

                        filtered_text = self._filter_thinking_content(final_text)
                        return {"response": filtered_text, "tools_used": tools_used, "session_id": session_id}

                    except Exception as fallback_error:
                        logger.error(f"Fallback also failed: {fallback_error}")
                        self.force_tool_usage = original_force_setting

                if iteration == 0:  # If first iteration fails, return error
                    raise
                # Otherwise, return what we have so far
                return {"response": f"Partial response due to error: {error_msg}", "tools_used": tools_used, "session_id": session_id}
        
        return {"response": "Response completed after maximum iterations.", "tools_used": tools_used, "session_id": session_id}

    def _filter_thinking_content(self, text: str) -> str:
        """
        Filter out thinking content from model responses.
        Removes content between <thinking> and </thinking> tags and other reasoning patterns.
        """
        import re

        if not text:
            return text

        # Remove thinking tags and their content
        # This pattern matches <thinking>...</thinking> including multiline content
        thinking_pattern = r'<thinking>.*?</thinking>'
        filtered_text = re.sub(thinking_pattern, '', text, flags=re.DOTALL | re.IGNORECASE)

        # Also remove other common reasoning patterns that might leak through
        # Remove content between <reasoning> and </reasoning> tags
        reasoning_pattern = r'<reasoning>.*?</reasoning>'
        filtered_text = re.sub(reasoning_pattern, '', filtered_text, flags=re.DOTALL | re.IGNORECASE)

        # Remove content between <analysis> and </analysis> tags
        analysis_pattern = r'<analysis>.*?</analysis>'
        filtered_text = re.sub(analysis_pattern, '', filtered_text, flags=re.DOTALL | re.IGNORECASE)

        # Clean up any extra whitespace that might be left
        filtered_text = re.sub(r'\n\s*\n\s*\n', '\n\n', filtered_text)  # Replace multiple newlines with double newlines
        filtered_text = filtered_text.strip()

        return filtered_text

    async def get_async_bedrock_runtime(self):
        """Get async bedrock runtime client."""
        try:
            import aioboto3
        except ImportError:
            raise ImportError("aioboto3 is required for async Bedrock operations. Install with: pip install aioboto3")
        
        session = aioboto3.Session()
        client = session.client("bedrock-runtime")
        return await client.__aenter__()

    async def _execute_tool_calls(self, tool_calls: List[Dict], session_id: str) -> List[Dict]:
        """Execute tool calls with enhanced error handling."""
        tools_used = []
        
        for tool_call in tool_calls:
            tool_name = tool_call.get("name")
            tool_input = tool_call.get("input", {})
            tool_use_id = tool_call.get("toolUseId")
            
            logger.info(f"Executing tool: {tool_name} with input: {tool_input}")
            
            server_name = self._find_server_for_tool(tool_name)
            
            if not server_name:
                tools_used.append({
                    "tool_name": tool_name,
                    "server_name": None,
                    "input": tool_input,
                    "success": False,
                    "error": f"Tool {tool_name} not found",
                    "session_id": session_id,
                    "toolUseId": tool_use_id,
                })
                continue
            
            try:
                result = await self.call_tool(server_name, tool_name, tool_input)
                
                usage = {
                    "tool_name": tool_name,
                    "server_name": server_name,
                    "input": tool_input,
                    "success": result.get("success", False),
                    "session_id": session_id,
                    "toolUseId": tool_use_id,
                }
                
                if usage["success"]:
                    usage["result"] = str(result.get("result", ""))
                else:
                    usage["error"] = result.get("error", "Unknown error")
                
                tools_used.append(usage)
                
            except Exception as e:
                logger.error(f"Tool execution exception for {tool_name}: {e}")
                tools_used.append({
                    "tool_name": tool_name,
                    "server_name": server_name,
                    "input": tool_input,
                    "success": False,
                    "error": f"Execution exception: {str(e)}",
                    "session_id": session_id,
                    "toolUseId": tool_use_id,
                })
        
        return tools_used

    def _find_server_for_tool(self, tool_name: str) -> Optional[str]:
        """Find server that provides a tool."""
        available_tools = self.get_available_tools()
        for data in available_tools.values():
            if data["tool"]["name"] == tool_name:
                return data["server"]
        return None
