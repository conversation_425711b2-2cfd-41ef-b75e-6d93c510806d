#!/usr/bin/env python3
"""
Test script for schema flattening functionality.
"""

import json
from enhanced_mcp_manager import EnhancedMCPMixin

class TestMCPManager(EnhancedMCPMixin):
    """Test implementation for schema flattening."""
    
    def __init__(self):
        super().__init__()
    
    def get_available_tools(self):
        """Mock method for testing."""
        return {}

def test_schema_flattening():
    """Test the schema flattening functionality."""
    
    manager = TestMCPManager()
    
    # Test complex schema with $defs
    complex_schema = {
        "$defs": {
            "DateRange": {
                "description": "Date range model for cost queries.",
                "properties": {
                    "start_date": {
                        "description": "The start date in YYYY-MM-DD format.",
                        "title": "Start Date",
                        "type": "string"
                    },
                    "end_date": {
                        "description": "The end date in YYYY-MM-DD format.",
                        "title": "End Date",
                        "type": "string"
                    }
                },
                "required": ["start_date", "end_date"],
                "title": "DateRange",
                "type": "object"
            }
        },
        "properties": {
            "date_range": {
                "$ref": "#/$defs/DateRange"
            },
            "optional_field": {
                "anyOf": [
                    {"type": "string"},
                    {"type": "null"}
                ],
                "default": None,
                "description": "An optional field"
            }
        },
        "required": ["date_range"],
        "title": "TestArguments",
        "type": "object"
    }
    
    print("Original complex schema:")
    print(json.dumps(complex_schema, indent=2))
    
    # Flatten the schema
    flattened = manager._flatten_schema_for_bedrock(complex_schema)
    
    print("\nFlattened schema:")
    print(json.dumps(flattened, indent=2))
    
    # Test compatibility check
    tools = [
        {
            "toolSpec": {
                "name": "test_tool",
                "inputSchema": {"json": complex_schema}
            }
        }
    ]
    
    is_compatible = manager._tools_are_bedrock_compatible(tools)
    print(f"\nTools are Bedrock compatible: {is_compatible}")
    
    # Test with simple schema
    simple_schema = {
        "type": "object",
        "properties": {
            "message": {
                "type": "string",
                "description": "A simple message"
            }
        },
        "required": ["message"]
    }
    
    simple_tools = [
        {
            "toolSpec": {
                "name": "simple_tool",
                "inputSchema": {"json": simple_schema}
            }
        }
    ]
    
    simple_compatible = manager._tools_are_bedrock_compatible(simple_tools)
    print(f"Simple tools are Bedrock compatible: {simple_compatible}")

if __name__ == "__main__":
    test_schema_flattening()
