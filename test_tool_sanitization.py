#!/usr/bin/env python3
"""
Test script to verify tool name sanitization for Bedrock compatibility.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_mcp_manager import EnhancedMCPMixin

class TestMixin(EnhancedMCPMixin):
    def __init__(self):
        # Initialize the mappings manually for testing
        self._tool_name_mapping = {}
        self._reverse_tool_name_mapping = {}
    
    def get_available_tools(self):
        # Mock available tools with the exact structure from the real system
        # The key is "server::tool_name" but the tool["name"] is just "tool_name"
        return {
            "cloudformation::get_resource_schema_information": {
                "server": "cloudformation",
                "tool": {
                    "name": "get_resource_schema_information",  # This is the clean name
                    "description": "Get resource schema information",
                    "input_schema": {"type": "object", "properties": {}}
                }
            },
            "cloudformation::list_resources": {
                "server": "cloudformation",
                "tool": {
                    "name": "list_resources",  # This is the clean name
                    "description": "List CloudFormation resources",
                    "input_schema": {"type": "object", "properties": {}}
                }
            },
            "cost-explorer::get_today_date": {
                "server": "cost-explorer",
                "tool": {
                    "name": "get_today_date",  # This is the clean name
                    "description": "Get today's date",
                    "input_schema": {"type": "object", "properties": {}}
                }
            }
        }

def test_tool_name_sanitization():
    """Test the tool name sanitization functionality."""
    print("Testing tool name sanitization...")

    mixin = TestMixin()

    # Test individual sanitization
    test_cases = [
        "cloudformation::get_resource_schema_information",
        "cost-explorer::get_today_date",
        "aws-pricing::get_pricing_info",
        "tool::with::multiple::colons",
        "tool-with-dashes",
        "tool_with_underscores",
        "tool with spaces",
        "tool@with#special$chars"
    ]

    print("\n=== Individual Sanitization Tests ===")
    for original in test_cases:
        sanitized = mixin._sanitize_tool_name_for_bedrock(original)
        print(f"'{original}' -> '{sanitized}'")

        # Verify it matches the Bedrock pattern
        import re
        pattern = r'^[a-zA-Z0-9_-]+$'
        if re.match(pattern, sanitized):
            print(f"  ✅ Valid Bedrock name")
        else:
            print(f"  ❌ Invalid Bedrock name")

    # Test tool config building
    print("\n=== Tool Config Building Test ===")
    tools_available = [
        "cloudformation::get_resource_schema_information",
        "cloudformation::list_resources",
        "cost-explorer::get_today_date"
    ]

    tool_config = mixin._build_tool_config_for_bedrock(tools_available)

    if tool_config:
        print(f"Generated tool config with {len(tool_config['tools'])} tools:")
        for i, tool in enumerate(tool_config['tools']):
            tool_spec = tool['toolSpec']
            print(f"  {i+1}. Name: '{tool_spec['name']}'")
            print(f"     Description: {tool_spec['description']}")

            # Verify name is valid for Bedrock
            import re
            pattern = r'^[a-zA-Z0-9_-]+$'
            if re.match(pattern, tool_spec['name']):
                print(f"     ✅ Valid Bedrock name")
            else:
                print(f"     ❌ Invalid Bedrock name")
    else:
        print("❌ No tool config generated")

    # Test mapping functionality
    print("\n=== Mapping Test ===")
    print("Tool name mappings:")
    for sanitized, original in mixin._tool_name_mapping.items():
        print(f"  '{sanitized}' -> '{original}'")

    print("\nReverse mappings:")
    for original, sanitized in mixin._reverse_tool_name_mapping.items():
        print(f"  '{original}' -> '{sanitized}'")

def test_problematic_scenario():
    """Test the scenario that would cause the original error."""
    print("\n" + "="*60)
    print("TESTING PROBLEMATIC SCENARIO")
    print("="*60)

    # Create a test mixin that simulates the bug where tool_key is used instead of tool["name"]
    class BuggyTestMixin(TestMixin):
        def _build_tool_config_for_bedrock_buggy(self, tools_available):
            """Simulate the bug where tool_key is used instead of tool['name']"""
            if not tools_available:
                return None

            available_tools = self.get_available_tools()
            tools = []

            for tool_key in tools_available:
                if tool_key not in available_tools:
                    continue

                tool_data = available_tools[tool_key]
                tool = tool_data["tool"]

                # BUG: Using tool_key instead of tool["name"] - this would cause the original error
                buggy_name = tool_key  # This is "cloudformation::get_resource_schema_information"

                input_schema = tool.get("input_schema") or {"type": "object", "properties": {}, "required": []}

                if "type" not in input_schema:
                    input_schema["type"] = "object"
                if "properties" not in input_schema:
                    input_schema["properties"] = {}

                tools.append({
                    "toolSpec": {
                        "name": buggy_name,  # This would cause the Bedrock validation error
                        "description": tool.get("description") or f"Tool from server {tool_data['server']}",
                        "inputSchema": {"json": input_schema}
                    }
                })

            return {"tools": tools, "toolChoice": {"auto": {}}}

    buggy_mixin = BuggyTestMixin()

    tools_available = [
        "cloudformation::get_resource_schema_information",
        "cloudformation::list_resources",
        "cost-explorer::get_today_date"
    ]

    print("Simulating the BUGGY behavior that would cause the original error:")
    buggy_config = buggy_mixin._build_tool_config_for_bedrock_buggy(tools_available)

    if buggy_config:
        print(f"Buggy tool config with {len(buggy_config['tools'])} tools:")
        for i, tool in enumerate(buggy_config['tools']):
            tool_spec = tool['toolSpec']
            print(f"  {i+1}. Name: '{tool_spec['name']}'")

            # Verify name is valid for Bedrock
            import re
            pattern = r'^[a-zA-Z0-9_-]+$'
            if re.match(pattern, tool_spec['name']):
                print(f"     ✅ Valid Bedrock name")
            else:
                print(f"     ❌ INVALID Bedrock name - would cause ValidationException!")

    print("\nNow testing the FIXED behavior:")
    fixed_config = buggy_mixin._build_tool_config_for_bedrock(tools_available)

    if fixed_config:
        print(f"Fixed tool config with {len(fixed_config['tools'])} tools:")
        for i, tool in enumerate(fixed_config['tools']):
            tool_spec = tool['toolSpec']
            print(f"  {i+1}. Name: '{tool_spec['name']}'")

            # Verify name is valid for Bedrock
            import re
            pattern = r'^[a-zA-Z0-9_-]+$'
            if re.match(pattern, tool_spec['name']):
                print(f"     ✅ Valid Bedrock name")
            else:
                print(f"     ❌ Invalid Bedrock name")

if __name__ == "__main__":
    test_tool_name_sanitization()
    test_problematic_scenario()
