#!/usr/bin/env python3
"""
Example usage of the Enhanced MCP Manager with forced tool usage.
This demonstrates how to integrate the forced tool usage feature into your application.
"""

import os
import asyncio
import logging
from typing import Dict, Any, List

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Example: Enable forced tool usage
os.environ["FORCE_TOOL_USAGE"] = "true"

class ExampleMCPManager:
    """
    Example implementation showing how to use the Enhanced MCP Manager
    with forced tool usage in a real application.
    """
    
    def __init__(self):
        # In a real application, you would import and inherit from:
        # from enhanced_mcp_manager import EnhancedMCPMixin
        # from your_mcp_client import MCPClientManager
        # class ExampleMCPManager(EnhancedMCPMixin, MCPClientManager):
        
        self.force_tool_usage = os.getenv("FORCE_TOOL_USAGE", "true").lower() == "true"
        logger.info(f"Initialized MCP Manager with force_tool_usage={self.force_tool_usage}")
    
    async def handle_user_query(self, user_message: str, session_id: str) -> Dict[str, Any]:
        """
        Handle a user query with forced tool usage.
        
        Args:
            user_message: The user's input message
            session_id: Unique session identifier
            
        Returns:
            Dictionary containing response and metadata
        """
        
        logger.info(f"Processing query for session {session_id}: {user_message[:50]}...")
        
        # In a real implementation, this would call:
        # return await self.chat_with_bedrock_with_context(
        #     message=user_message,
        #     session_id=session_id,
        #     tools_available=self.get_available_tool_names()
        # )
        
        # Mock response for demonstration
        tools_used = []
        if self.force_tool_usage:
            # Simulate forced tool usage
            tools_used = [
                {"tool_name": "get_current_time", "success": True, "result": "2024-01-15 14:30:00 UTC"},
                {"tool_name": "search_knowledge", "success": True, "result": "Found relevant information"}
            ]
            response = f"Based on fresh data retrieved using {len(tools_used)} tools: {user_message}"
        else:
            response = f"Response without forced tools: {user_message}"
        
        return {
            "response": response,
            "tools_used": tools_used,
            "session_id": session_id,
            "forced_tools": self.force_tool_usage
        }

async def demonstrate_forced_tools():
    """Demonstrate the forced tool usage feature."""
    
    manager = ExampleMCPManager()
    
    # Example queries
    queries = [
        "What's the current weather in New York?",
        "Tell me about the latest news",
        "What time is it?",
        "How do I cook pasta?",
        "What's the stock price of AAPL?"
    ]
    
    print("=" * 80)
    print("DEMONSTRATION: Forced Tool Usage Feature")
    print("=" * 80)
    
    for i, query in enumerate(queries, 1):
        print(f"\n--- Query {i}: {query} ---")
        
        result = await manager.handle_user_query(query, f"session_{i}")
        
        print(f"Response: {result['response']}")
        print(f"Tools used: {len(result['tools_used'])}")
        print(f"Forced tools enabled: {result['forced_tools']}")
        
        if result['tools_used']:
            print("Tool details:")
            for tool in result['tools_used']:
                print(f"  - {tool['tool_name']}: {'✓' if tool['success'] else '✗'}")

def demonstrate_configuration():
    """Demonstrate different configuration options."""
    
    print("\n" + "=" * 80)
    print("CONFIGURATION DEMONSTRATION")
    print("=" * 80)
    
    configurations = [
        ("true", "Forced tool usage enabled"),
        ("false", "Optional tool usage"),
        ("TRUE", "Case insensitive - enabled"),
        ("False", "Case insensitive - disabled"),
        ("", "Empty value - defaults to true"),
    ]
    
    for env_value, description in configurations:
        # Set environment variable
        if env_value == "":
            os.environ.pop("FORCE_TOOL_USAGE", None)
        else:
            os.environ["FORCE_TOOL_USAGE"] = env_value
        
        # Create new manager instance
        manager = ExampleMCPManager()
        
        print(f"\nFORCE_TOOL_USAGE='{env_value}' -> {description}")
        print(f"  Actual setting: force_tool_usage={manager.force_tool_usage}")

def show_integration_example():
    """Show how to integrate this into an existing application."""
    
    print("\n" + "=" * 80)
    print("INTEGRATION EXAMPLE")
    print("=" * 80)
    
    integration_code = '''
# 1. Import the enhanced MCP manager
from enhanced_mcp_manager import EnhancedMCPMixin
from your_existing_mcp_client import MCPClientManager

# 2. Create your enhanced manager class
class YourMCPManager(EnhancedMCPMixin, MCPClientManager):
    def __init__(self):
        super().__init__()
        # Your existing initialization code here

# 3. Use the enhanced manager in your application
async def handle_chat_request(user_message: str, session_id: str):
    manager = YourMCPManager()
    
    # This will now use forced tool usage if FORCE_TOOL_USAGE=true
    result = await manager.chat_with_bedrock_with_context(
        message=user_message,
        session_id=session_id,
        tools_available=manager.get_available_tool_names()
    )
    
    return result

# 4. Configure the behavior via environment variables
import os
os.environ["FORCE_TOOL_USAGE"] = "true"  # Enable forced tools
# or
os.environ["FORCE_TOOL_USAGE"] = "false"  # Make tools optional
'''
    
    print("Integration steps:")
    print(integration_code)

async def main():
    """Main demonstration function."""
    
    print("Enhanced MCP Manager - Forced Tool Usage Example")
    print("This example demonstrates the forced tool usage feature.")
    
    try:
        # Demonstrate forced tool usage
        await demonstrate_forced_tools()
        
        # Demonstrate configuration options
        demonstrate_configuration()
        
        # Show integration example
        show_integration_example()
        
        print("\n" + "=" * 80)
        print("SUMMARY")
        print("=" * 80)
        print("✓ Forced tool usage ensures fresh data on every query")
        print("✓ Configurable via FORCE_TOOL_USAGE environment variable")
        print("✓ Backward compatible - defaults to enabled")
        print("✓ Easy to integrate into existing applications")
        print("✓ Provides clear indicators when tools are/aren't used")
        
        print("\nNext steps:")
        print("1. Set FORCE_TOOL_USAGE=true in your environment")
        print("2. Integrate EnhancedMCPMixin into your MCP manager")
        print("3. Monitor tool usage patterns in your logs")
        print("4. Adjust configuration based on your use case")
        
    except Exception as e:
        logger.error(f"Demonstration failed: {e}")
        print(f"\n✗ Error during demonstration: {e}")

if __name__ == "__main__":
    asyncio.run(main())
