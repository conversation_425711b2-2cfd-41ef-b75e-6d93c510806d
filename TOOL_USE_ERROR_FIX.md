# Tool Use Error Fix for MCP Bedrock Manager

## Problem
When `FORCE_TOOL_USAGE=true` was enabled, the system encountered this error:
```
Model produced invalid sequence as part of ToolUse. Please refer to the model tool use troubleshooting guide.
```

## Root Cause
The error was caused by complex JSON schemas with `$defs` references and nested structures that Bedrock's tool use system couldn't handle reliably when forced to use tools. The AWS Bedrock model struggled to generate valid tool calls for schemas containing:

- `$defs` references (e.g., `"$ref": "#/$defs/DateRange"`)
- Complex `anyOf`/`oneOf` structures
- Deep nesting levels
- Very large schema definitions

## Solution Implemented

### 1. Schema Flattening (`_flatten_schema_for_bedrock`)
- **Removes `$defs` references** and inlines simple definitions
- **Simplifies `anyOf` structures** to single types (picks first non-null type)
- **Converts `$ref` references** to simple string types when inlining fails
- **Ensures all properties have explicit types**

### 2. Compatibility Detection (`_tools_are_bedrock_compatible`)
- **Analyzes tool schemas** before applying forced tool usage
- **Detects complexity indicators**:
  - Presence of `$defs` or `$ref`
  - Schema size > 5000 characters
  - Nesting depth > 4 levels
- **Falls back to "auto" tool choice** when complex schemas detected

### 3. Error Recovery Mechanism
- **Catches ModelErrorException** with ToolUse errors
- **Temporarily disables forced tool usage** for the failing request
- **Retries with "auto" tool choice** as fallback
- **Restores original settings** after recovery
- **Provides clear user feedback** about the fallback

### 4. Enhanced Logging
- **Logs tool choice decisions** (forced vs auto)
- **Warns about complex schemas** that trigger fallbacks
- **Tracks error recovery attempts**

## How It Works

### Normal Operation (Simple Schemas)
```
User Query → Simple Tool Schemas → Force Tool Choice "any" → Model Uses Tools → Response
```

### Fallback for Complex Schemas
```
User Query → Complex Tool Schemas Detected → Use Tool Choice "auto" → Model Decides → Response
```

### Error Recovery
```
User Query → Force Tool Choice "any" → ModelErrorException → Disable Forcing → Retry with "auto" → Response
```

## Configuration

The system automatically handles schema complexity, but you can still control the base behavior:

```bash
# Enable forced tool usage (with automatic fallbacks)
export FORCE_TOOL_USAGE=true

# Disable forced tool usage entirely
export FORCE_TOOL_USAGE=false
```

## Benefits

1. **Reliability**: No more tool use errors from complex schemas
2. **Automatic Fallback**: Graceful degradation when schemas are too complex
3. **Transparency**: Clear logging about when and why fallbacks occur
4. **Compatibility**: Works with existing MCP servers without modification
5. **Performance**: Schema flattening reduces complexity for better model performance

## Example Schema Transformation

**Before (Complex):**
```json
{
  "$defs": {
    "DateRange": {
      "properties": {
        "start_date": {"type": "string"},
        "end_date": {"type": "string"}
      }
    }
  },
  "properties": {
    "date_range": {"$ref": "#/$defs/DateRange"},
    "optional": {
      "anyOf": [{"type": "string"}, {"type": "null"}]
    }
  }
}
```

**After (Flattened):**
```json
{
  "properties": {
    "date_range": {
      "type": "object",
      "properties": {
        "start_date": {"type": "string"},
        "end_date": {"type": "string"}
      }
    },
    "optional": {
      "type": "string"
    }
  }
}
```

## Testing

Run the test script to verify schema flattening:
```bash
python test_schema_flattening.py
```

This will show how complex schemas are simplified and whether they're considered Bedrock-compatible.

## Result

The bot now handles tool use errors gracefully and can answer questions reliably, even with complex MCP server tool schemas. The forced tool usage feature works when possible, with automatic fallbacks ensuring system stability.
